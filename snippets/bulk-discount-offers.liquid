<div class="bulk-discount-offers">
  <div class="bulk-discount-offers__header">
    <p class="bulk-discount-offers__subtitle">Taxes included. <a href="#" class="bulk-discount-offers__shipping-link">Shipping</a> calculated at checkout.</p>
    <h3 class="bulk-discount-offers__title">MİKTAR İNDİRİMLERİ</h3>
  </div>
  
  <div class="bulk-discount-offers__list">
    <div class="bulk-discount-offers__item">
      <div class="bulk-discount-offers__content">
        <h4 class="bulk-discount-offers__item-title">2'li Paket - %10 İndirim</h4>
        <div class="bulk-discount-offers__pricing">
          <span class="bulk-discount-offers__price">1,080.00TL</span>
          <span class="bulk-discount-offers__original-price">1,200.00TL</span>
        </div>
        <div class="bulk-discount-offers__savings">120.00TL tasarruf</div>
        <div class="bulk-discount-offers__unit-price">Adet başı: 540.00TL</div>
      </div>
      <button class="bulk-discount-offers__button" type="button" data-quantity="2" data-price="108000">
        SEPETE EKLE
      </button>
    </div>

    <div class="bulk-discount-offers__item">
      <div class="bulk-discount-offers__content">
        <h4 class="bulk-discount-offers__item-title">3'lü Paket - %15 İndirim</h4>
        <div class="bulk-discount-offers__pricing">
          <span class="bulk-discount-offers__price">1,530.00TL</span>
          <span class="bulk-discount-offers__original-price">1,800.00TL</span>
        </div>
        <div class="bulk-discount-offers__savings">270.00TL tasarruf</div>
        <div class="bulk-discount-offers__unit-price">Adet başı: 510.00TL</div>
      </div>
      <button class="bulk-discount-offers__button" type="button" data-quantity="3" data-price="153000">
        SEPETE EKLE
      </button>
    </div>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const bulkButtons = document.querySelectorAll('.bulk-discount-offers__button');

  function updateQuantityEverywhere(quantity) {
    // Find the main quantity input
    const quantityInput = document.querySelector('input[name="quantity"]') ||
                         document.querySelector('.quantity__input') ||
                         document.querySelector('[id*="Quantity"]');

    if (quantityInput) {
      // Set the value
      quantityInput.value = quantity;

      // Get the quantity-input custom element
      const quantityElement = quantityInput.closest('quantity-input');

      if (quantityElement) {
        // Call validateQtyRules to update button states
        if (quantityElement.validateQtyRules) {
          quantityElement.validateQtyRules();
        }

        // Trigger the change event through the custom element
        const changeEvent = new Event('change', { bubbles: true });
        quantityInput.dispatchEvent(changeEvent);
      } else {
        // Fallback for regular inputs
        ['input', 'change', 'blur'].forEach(eventType => {
          quantityInput.dispatchEvent(new Event(eventType, { bubbles: true }));
        });
      }
    }

    // Update quantity discount selector
    const quantityRadio = document.querySelector(`input[name="quantity_option"][value="${quantity}"]`);
    if (quantityRadio) {
      quantityRadio.checked = true;
      quantityRadio.dispatchEvent(new Event('change', { bubbles: true }));
    }

    // Also update any other quantity displays
    const allQuantityInputs = document.querySelectorAll('input[name="quantity"], .quantity__input');
    allQuantityInputs.forEach(input => {
      if (input !== quantityInput) {
        input.value = quantity;
        const changeEvent = new Event('change', { bubbles: true });
        input.dispatchEvent(changeEvent);
      }
    });

    console.log('Bulk offer: Quantity updated to:', quantity, 'Input found:', !!quantityInput);
  }

  bulkButtons.forEach(button => {
    button.addEventListener('click', function() {
      const quantity = parseInt(this.dataset.quantity);
      const price = this.dataset.price;

      updateQuantityEverywhere(quantity);

      // Scroll to add to cart button with a small delay
      setTimeout(() => {
        const buyButtons = document.querySelector('.product-form__buttons, .product-form__cart-submit, [data-testid="add-to-cart"]');
        if (buyButtons) {
          buyButtons.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
      }, 100);
    });
  });
});
</script>
