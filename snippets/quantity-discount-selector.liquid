{%- assign heading = block.settings.heading | default: 'SINIRLI SAYIDA BUGÜNE ÖZEL' -%}

<div class="quantity-discount" {{ block.shopify_attributes }}>
  <h3 class="quantity-discount__heading">{{ heading }}</h3>
  
  <div class="quantity-discount__options">
    {%- comment -%} Tier 1 {%- endcomment -%}
    <div class="quantity-discount__option"
         data-quantity="1"
         data-price="60000"
         data-original-price="60000"
         data-variant-id="{{ product.selected_or_first_available_variant.id }}">

      <div class="quantity-discount__header">
        <div class="quantity-discount__left">
          <input type="radio"
                 name="quantity_option"
                 value="1"
                 id="qty-1-{{ section.id }}"
                 checked>
          <label for="qty-1-{{ section.id }}">1 ADET</label>
          <span class="quantity-discount__badge">ÜCRETSİZ KARGO</span>
        </div>

        <div class="quantity-discount__right">
          <div class="quantity-discount__pricing">
            <span class="quantity-discount__price">600.00TL</span>
          </div>
        </div>
      </div>
    </div>
    
    {%- comment -%} Tier 2 {%- endcomment -%}
    <div class="quantity-discount__option popular"
         data-quantity="2"
         data-price="108000"
         data-original-price="120000"
         data-variant-id="{{ product.selected_or_first_available_variant.id }}">

      <div class="quantity-discount__header">
        <div class="quantity-discount__left">
          <input type="radio"
                 name="quantity_option"
                 value="2"
                 id="qty-2-{{ section.id }}">
          <label for="qty-2-{{ section.id }}">2 ADET</label>
          <span class="quantity-discount__badge">ÜCRETSİZ KARGO</span>
        </div>

        <div class="quantity-discount__right">
          <div class="quantity-discount__pricing">
            <span class="quantity-discount__price">1,080.00TL</span>
            <span class="quantity-discount__original-price">1,200.00TL</span>
          </div>
        </div>
      </div>

      <div class="quantity-discount__savings">%10 İndirim - 120.00TL tasarruf</div>
    </div>
    
    {%- comment -%} Tier 3 {%- endcomment -%}
    <div class="quantity-discount__option"
         data-quantity="3"
         data-price="153000"
         data-original-price="180000"
         data-variant-id="{{ product.selected_or_first_available_variant.id }}">

      <div class="quantity-discount__header">
        <div class="quantity-discount__left">
          <input type="radio"
                 name="quantity_option"
                 value="3"
                 id="qty-3-{{ section.id }}">
          <label for="qty-3-{{ section.id }}">3 ADET</label>
          <span class="quantity-discount__badge">ÜCRETSİZ KARGO</span>
        </div>

        <div class="quantity-discount__right">
          <div class="quantity-discount__pricing">
            <span class="quantity-discount__price">1,530.00TL</span>
            <span class="quantity-discount__original-price">1,800.00TL</span>
          </div>
        </div>
      </div>

      <div class="quantity-discount__savings">%15 İndirim - 270.00TL tasarruf</div>
    </div>
  </div>
</div>

<script>
// Wait for everything to load, including product form
function initQuantityDiscountSelector() {
  const quantityOptions = document.querySelectorAll('.quantity-discount__option input[type="radio"]');

  function updateQuantity(quantity) {
    console.log('=== UPDATING QUANTITY TO:', quantity, '===');

    // Find the product form first
    const productForm = document.querySelector('form[action*="/cart/add"]') ||
                       document.querySelector('.product-form form') ||
                       document.querySelector('form[data-type="add-to-cart-form"]');

    if (!productForm) {
      console.log('No product form found!');
      return;
    }

    console.log('Found product form:', productForm);

    // Remove any existing custom quantity inputs
    const existingCustomQuantity = productForm.querySelector('input[name="quantity"][data-quantity-discount]');
    if (existingCustomQuantity) {
      existingCustomQuantity.remove();
    }

    // Always create a hidden quantity input to ensure the correct quantity is sent
    const hiddenQuantity = document.createElement('input');
    hiddenQuantity.type = 'hidden';
    hiddenQuantity.name = 'quantity';
    hiddenQuantity.value = quantity;
    hiddenQuantity.setAttribute('data-quantity-discount', 'true');
    productForm.appendChild(hiddenQuantity);

    console.log('Created hidden quantity input with value:', quantity);

    // Also update any visible quantity inputs for UI consistency
    const visibleQuantityInputs = document.querySelectorAll('input[name="quantity"]:not([data-quantity-discount])');
    visibleQuantityInputs.forEach((input, index) => {
      console.log(`Updating visible input ${index}:`, input);
      input.value = quantity;

      // Trigger events for UI updates
      const events = ['input', 'change'];
      events.forEach(eventType => {
        input.dispatchEvent(new Event(eventType, { bubbles: true }));
      });
    });

    console.log('=== QUANTITY UPDATE COMPLETE ===');
  }

  let selectedQuantity = 1; // Track selected quantity

  quantityOptions.forEach(option => {
    option.addEventListener('change', function() {
      if (this.checked) {
        selectedQuantity = parseInt(this.value);
        updateQuantity(selectedQuantity);
      }
    });
  });

  // Add form submit listener to ensure quantity is correct
  const productForm = document.querySelector('form[action*="/cart/add"]') ||
                     document.querySelector('.product-form form') ||
                     document.querySelector('form[data-type="add-to-cart-form"]');

  if (productForm) {
    productForm.addEventListener('submit', function(e) {
      console.log('Form submitting with quantity:', selectedQuantity);

      // Ensure the hidden quantity input has the correct value
      let quantityInput = productForm.querySelector('input[name="quantity"][data-quantity-discount]');
      if (quantityInput) {
        quantityInput.value = selectedQuantity;
      } else {
        // Create it if it doesn't exist
        quantityInput = document.createElement('input');
        quantityInput.type = 'hidden';
        quantityInput.name = 'quantity';
        quantityInput.value = selectedQuantity;
        quantityInput.setAttribute('data-quantity-discount', 'true');
        productForm.appendChild(quantityInput);
      }

      console.log('Final quantity value being sent:', quantityInput.value);
    });
  }

  // Initialize with first option
  if (quantityOptions.length > 0) {
    quantityOptions[0].checked = true;
    selectedQuantity = parseInt(quantityOptions[0].value);
    updateQuantity(selectedQuantity);
  }
}

// Try multiple times to ensure everything is loaded
document.addEventListener('DOMContentLoaded', function() {
  initQuantityDiscountSelector();
});

// Also try after a short delay
setTimeout(initQuantityDiscountSelector, 500);

// And try when the page is fully loaded
window.addEventListener('load', function() {
  setTimeout(initQuantityDiscountSelector, 1000);
});
</script>
