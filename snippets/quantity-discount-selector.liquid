{%- assign heading = block.settings.heading | default: 'SINIRLI SAYIDA BUGÜNE ÖZEL' -%}

<div class="quantity-discount" {{ block.shopify_attributes }}>
  <h3 class="quantity-discount__heading">{{ heading }}</h3>
  
  <div class="quantity-discount__options">
    {%- comment -%} Tier 1 {%- endcomment -%}
    <div class="quantity-discount__option"
         data-quantity="1"
         data-price="60000"
         data-original-price="60000"
         data-variant-id="{{ product.selected_or_first_available_variant.id }}">

      <div class="quantity-discount__header">
        <div class="quantity-discount__left">
          <input type="radio"
                 name="quantity_option"
                 value="1"
                 id="qty-1-{{ section.id }}"
                 checked>
          <label for="qty-1-{{ section.id }}">1 ADET</label>
          <span class="quantity-discount__badge">ÜCRETSİZ KARGO</span>
        </div>

        <div class="quantity-discount__right">
          <div class="quantity-discount__pricing">
            <span class="quantity-discount__price">600.00TL</span>
          </div>
        </div>
      </div>
    </div>
    
    {%- comment -%} Tier 2 {%- endcomment -%}
    <div class="quantity-discount__option popular"
         data-quantity="2"
         data-price="108000"
         data-original-price="120000"
         data-variant-id="{{ product.selected_or_first_available_variant.id }}">

      <div class="quantity-discount__header">
        <div class="quantity-discount__left">
          <input type="radio"
                 name="quantity_option"
                 value="2"
                 id="qty-2-{{ section.id }}">
          <label for="qty-2-{{ section.id }}">2 ADET</label>
          <span class="quantity-discount__badge">ÜCRETSİZ KARGO</span>
        </div>

        <div class="quantity-discount__right">
          <div class="quantity-discount__pricing">
            <span class="quantity-discount__price">1,080.00TL</span>
            <span class="quantity-discount__original-price">1,200.00TL</span>
          </div>
        </div>
      </div>

      <div class="quantity-discount__savings">%10 İndirim - 120.00TL tasarruf</div>
    </div>
    
    {%- comment -%} Tier 3 {%- endcomment -%}
    <div class="quantity-discount__option"
         data-quantity="3"
         data-price="153000"
         data-original-price="180000"
         data-variant-id="{{ product.selected_or_first_available_variant.id }}">

      <div class="quantity-discount__header">
        <div class="quantity-discount__left">
          <input type="radio"
                 name="quantity_option"
                 value="3"
                 id="qty-3-{{ section.id }}">
          <label for="qty-3-{{ section.id }}">3 ADET</label>
          <span class="quantity-discount__badge">ÜCRETSİZ KARGO</span>
        </div>

        <div class="quantity-discount__right">
          <div class="quantity-discount__pricing">
            <span class="quantity-discount__price">1,530.00TL</span>
            <span class="quantity-discount__original-price">1,800.00TL</span>
          </div>
        </div>
      </div>

      <div class="quantity-discount__savings">%15 İndirim - 270.00TL tasarruf</div>
    </div>
  </div>
</div>

<script>
// Wait for everything to load, including product form
function initQuantityDiscountSelector() {
  const quantityOptions = document.querySelectorAll('.quantity-discount__option input[type="radio"]');

  function updateQuantity(quantity) {
    console.log('=== UPDATING QUANTITY TO:', quantity, '===');

    // Find ALL forms on the page
    const allForms = document.querySelectorAll('form');
    console.log('ALL FORMS ON PAGE:', allForms.length);
    allForms.forEach((form, i) => {
      console.log(`Form ${i}:`, {
        action: form.action,
        method: form.method,
        id: form.id,
        className: form.className,
        innerHTML: form.innerHTML.substring(0, 200) + '...'
      });
    });

    // Find the product form with multiple selectors
    const productForm = document.querySelector('form[action*="/cart/add"]') ||
                       document.querySelector('.product-form form') ||
                       document.querySelector('form[data-type="add-to-cart-form"]') ||
                       document.querySelector('product-form form') ||
                       document.querySelector('form');

    if (!productForm) {
      console.log('No product form found!');
      return;
    }

    console.log('Found product form:', productForm);
    console.log('Form action:', productForm.action);
    console.log('Form method:', productForm.method);

    // Check all inputs in the form
    const allInputsInForm = productForm.querySelectorAll('input');
    console.log('ALL INPUTS IN FORM:', allInputsInForm.length);
    allInputsInForm.forEach((input, i) => {
      console.log(`Form Input ${i}:`, {
        type: input.type,
        name: input.name,
        value: input.value,
        id: input.id
      });
    });

    // Remove any existing custom quantity inputs
    const existingCustomQuantity = productForm.querySelector('input[name="quantity"][data-quantity-discount]');
    if (existingCustomQuantity) {
      console.log('Removing existing custom quantity input');
      existingCustomQuantity.remove();
    }

    // Remove any existing quantity inputs to avoid conflicts
    const existingQuantityInputs = productForm.querySelectorAll('input[name="quantity"]');
    console.log('Found existing quantity inputs:', existingQuantityInputs.length);
    existingQuantityInputs.forEach((input, i) => {
      console.log(`Removing existing quantity input ${i}:`, input);
      input.remove();
    });

    // Always create a hidden quantity input to ensure the correct quantity is sent
    const hiddenQuantity = document.createElement('input');
    hiddenQuantity.type = 'hidden';
    hiddenQuantity.name = 'quantity';
    hiddenQuantity.value = quantity;
    hiddenQuantity.setAttribute('data-quantity-discount', 'true');
    productForm.appendChild(hiddenQuantity);

    console.log('Created hidden quantity input with value:', quantity);
    console.log('Hidden input element:', hiddenQuantity);

    console.log('=== QUANTITY UPDATE COMPLETE ===');
  }

  let selectedQuantity = 1; // Track selected quantity

  quantityOptions.forEach(option => {
    option.addEventListener('change', function() {
      if (this.checked) {
        selectedQuantity = parseInt(this.value);
        updateQuantity(selectedQuantity);
      }
    });
  });

  // Override FormData to ensure our quantity is always used
  const originalFormData = window.FormData;
  let formDataOverrideActive = false;

  function activateFormDataOverride() {
    if (formDataOverrideActive) return;
    formDataOverrideActive = true;

    window.FormData = function(form) {
      const formData = new originalFormData(form);

      // If this is the product form, override the quantity
      if (form && (form.action.includes('/cart/add') || form.getAttribute('data-type') === 'add-to-cart-form')) {
        console.log('🔄 FormData override: Intercepting form data for cart add');
        console.log('🔄 Original quantity in form:', formData.get('quantity'));
        console.log('🔄 Overriding with selected quantity:', selectedQuantity);

        // Remove existing quantity
        formData.delete('quantity');
        // Add our quantity
        formData.append('quantity', selectedQuantity);

        console.log('🔄 Final quantity in FormData:', formData.get('quantity'));
      }

      return formData;
    };

    // Copy static methods
    Object.setPrototypeOf(window.FormData, originalFormData);
    Object.setPrototypeOf(window.FormData.prototype, originalFormData.prototype);
  }

  // Add form submit listener to ensure quantity is correct
  const productFormForSubmit = document.querySelector('form[action*="/cart/add"]') ||
                               document.querySelector('.product-form form') ||
                               document.querySelector('form[data-type="add-to-cart-form"]') ||
                               document.querySelector('product-form form') ||
                               document.querySelector('form');

  if (productFormForSubmit) {
    console.log('Adding submit listener to form:', productFormForSubmit);

    // Activate FormData override
    activateFormDataOverride();

    productFormForSubmit.addEventListener('submit', function(e) {
      console.log('🚀 FORM SUBMITTING! Selected quantity:', selectedQuantity);
      console.log('🚀 Form being submitted:', this);

      // Remove any existing quantity inputs to avoid conflicts
      const existingQuantityInputs = this.querySelectorAll('input[name="quantity"]');
      console.log('🚀 Removing existing quantity inputs:', existingQuantityInputs.length);
      existingQuantityInputs.forEach(input => input.remove());

      // Create fresh quantity input
      const quantityInput = document.createElement('input');
      quantityInput.type = 'hidden';
      quantityInput.name = 'quantity';
      quantityInput.value = selectedQuantity;
      quantityInput.setAttribute('data-quantity-discount', 'true');
      this.appendChild(quantityInput);

      console.log('🚀 Created new quantity input with value:', quantityInput.value);
    });
  } else {
    console.log('❌ No product form found for submit listener!');
  }

  // Initialize with first option
  if (quantityOptions.length > 0) {
    quantityOptions[0].checked = true;
    selectedQuantity = parseInt(quantityOptions[0].value);
    updateQuantity(selectedQuantity);
  }
}

// Try multiple times to ensure everything is loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('🔄 DOMContentLoaded - initializing quantity discount selector');
  initQuantityDiscountSelector();
});

// Also try after a short delay
setTimeout(function() {
  console.log('🔄 500ms timeout - initializing quantity discount selector');
  initQuantityDiscountSelector();
}, 500);

// And try when the page is fully loaded
window.addEventListener('load', function() {
  setTimeout(function() {
    console.log('🔄 Window load + 1000ms - initializing quantity discount selector');
    initQuantityDiscountSelector();
  }, 1000);
});

// Also try after 2 seconds to be extra sure
setTimeout(function() {
  console.log('🔄 2000ms timeout - final initialization attempt');
  initQuantityDiscountSelector();
}, 2000);

// Global function to manually trigger if needed
window.reinitQuantitySelector = function() {
  console.log('🔄 Manual reinit triggered');
  initQuantityDiscountSelector();
};
</script>
