{%- assign heading = block.settings.heading | default: 'SINIRLI SAYIDA BUGÜNE ÖZEL' -%}

<div class="quantity-discount" {{ block.shopify_attributes }}>
  <h3 class="quantity-discount__heading">{{ heading }}</h3>
  
  <div class="quantity-discount__options">
    {%- comment -%} Tier 1 {%- endcomment -%}
    <div class="quantity-discount__option"
         data-quantity="1"
         data-price="60000"
         data-original-price="60000"
         data-variant-id="{{ product.selected_or_first_available_variant.id }}">

      <div class="quantity-discount__header">
        <div class="quantity-discount__left">
          <input type="radio"
                 name="quantity_option"
                 value="1"
                 id="qty-1-{{ section.id }}"
                 checked>
          <label for="qty-1-{{ section.id }}">1 ADET</label>
          <span class="quantity-discount__badge">ÜCRETSİZ KARGO</span>
        </div>

        <div class="quantity-discount__right">
          <div class="quantity-discount__pricing">
            <span class="quantity-discount__price">600.00TL</span>
          </div>
        </div>
      </div>
    </div>
    
    {%- comment -%} Tier 2 {%- endcomment -%}
    <div class="quantity-discount__option popular"
         data-quantity="2"
         data-price="108000"
         data-original-price="120000"
         data-variant-id="{{ product.selected_or_first_available_variant.id }}">

      <div class="quantity-discount__header">
        <div class="quantity-discount__left">
          <input type="radio"
                 name="quantity_option"
                 value="2"
                 id="qty-2-{{ section.id }}">
          <label for="qty-2-{{ section.id }}">2 ADET</label>
          <span class="quantity-discount__badge">ÜCRETSİZ KARGO</span>
        </div>

        <div class="quantity-discount__right">
          <div class="quantity-discount__pricing">
            <span class="quantity-discount__price">1,080.00TL</span>
            <span class="quantity-discount__original-price">1,200.00TL</span>
          </div>
        </div>
      </div>

      <div class="quantity-discount__savings">%10 İndirim - 120.00TL tasarruf</div>
    </div>
    
    {%- comment -%} Tier 3 {%- endcomment -%}
    <div class="quantity-discount__option"
         data-quantity="3"
         data-price="153000"
         data-original-price="180000"
         data-variant-id="{{ product.selected_or_first_available_variant.id }}">

      <div class="quantity-discount__header">
        <div class="quantity-discount__left">
          <input type="radio"
                 name="quantity_option"
                 value="3"
                 id="qty-3-{{ section.id }}">
          <label for="qty-3-{{ section.id }}">3 ADET</label>
          <span class="quantity-discount__badge">ÜCRETSİZ KARGO</span>
        </div>

        <div class="quantity-discount__right">
          <div class="quantity-discount__pricing">
            <span class="quantity-discount__price">1,530.00TL</span>
            <span class="quantity-discount__original-price">1,800.00TL</span>
          </div>
        </div>
      </div>

      <div class="quantity-discount__savings">%15 İndirim - 270.00TL tasarruf</div>
    </div>
  </div>
</div>

<script>
// Wait for everything to load, including product form
function initQuantityDiscountSelector() {
  const quantityOptions = document.querySelectorAll('.quantity-discount__option input[type="radio"]');

  function updateQuantity(quantity) {
    console.log('=== UPDATING QUANTITY TO:', quantity, '===');

    // First, let's see ALL inputs on the page
    const allInputs = document.querySelectorAll('input');
    console.log('ALL INPUTS ON PAGE:', allInputs.length);
    allInputs.forEach((input, i) => {
      console.log(`Input ${i}:`, {
        type: input.type,
        name: input.name,
        id: input.id,
        className: input.className,
        value: input.value,
        element: input
      });
    });

    // Find ALL possible quantity inputs
    const selectors = [
      'input[name="quantity"]',
      '.quantity__input',
      '[id*="Quantity"]',
      '[id*="quantity"]',
      'input[type="number"]'
    ];

    let foundInputs = [];
    selectors.forEach(selector => {
      console.log('Trying selector:', selector);
      const inputs = document.querySelectorAll(selector);
      console.log('Found with selector:', inputs.length, inputs);
      inputs.forEach(input => {
        if (!foundInputs.includes(input)) {
          foundInputs.push(input);
        }
      });
    });

    console.log('Found quantity inputs:', foundInputs.length, foundInputs);

    if (foundInputs.length === 0) {
      console.log('NO QUANTITY INPUTS FOUND! Creating fallback...');

      // Find the product form
      const productForm = document.querySelector('form[action*="/cart/add"]') ||
                         document.querySelector('.product-form') ||
                         document.querySelector('form');

      if (productForm) {
        console.log('Found product form:', productForm);

        // Remove any existing quantity inputs we might have added
        const existingHidden = productForm.querySelector('input[name="quantity"][data-custom]');
        if (existingHidden) {
          existingHidden.remove();
        }

        // Create a hidden quantity input
        const hiddenQuantity = document.createElement('input');
        hiddenQuantity.type = 'hidden';
        hiddenQuantity.name = 'quantity';
        hiddenQuantity.value = quantity;
        hiddenQuantity.setAttribute('data-custom', 'true');
        productForm.appendChild(hiddenQuantity);

        console.log('Created hidden quantity input with value:', quantity);
      } else {
        console.log('No product form found either!');
      }
    } else {
      foundInputs.forEach((input, index) => {
        console.log(`Input ${index}:`, input.name, input.id, input.className, 'current value:', input.value);

        // Set the value
        input.value = quantity;

        // Get the quantity-input custom element
        const quantityElement = input.closest('quantity-input');

        if (quantityElement) {
          console.log('Found quantity-input custom element for input', index);

          // Call validateQtyRules to update button states
          if (quantityElement.validateQtyRules) {
            quantityElement.validateQtyRules();
          }

          // Trigger the change event through the custom element
          const changeEvent = new Event('change', { bubbles: true });
          input.dispatchEvent(changeEvent);
        } else {
          console.log('No custom element found for input', index, 'using fallback');
          // Fallback for regular inputs
          ['input', 'change', 'blur'].forEach(eventType => {
            input.dispatchEvent(new Event(eventType, { bubbles: true }));
          });
        }

        console.log(`Input ${index} after update:`, input.value);
      });
    }

    console.log('=== QUANTITY UPDATE COMPLETE ===');
  }

  quantityOptions.forEach(option => {
    option.addEventListener('change', function() {
      if (this.checked) {
        const quantity = parseInt(this.value);
        updateQuantity(quantity);
      }
    });
  });

  // Initialize with first option
  if (quantityOptions.length > 0) {
    quantityOptions[0].checked = true;
    const initialQuantity = parseInt(quantityOptions[0].value);
    updateQuantity(initialQuantity);
  }
}

// Try multiple times to ensure everything is loaded
document.addEventListener('DOMContentLoaded', function() {
  initQuantityDiscountSelector();
});

// Also try after a short delay
setTimeout(initQuantityDiscountSelector, 500);

// And try when the page is fully loaded
window.addEventListener('load', function() {
  setTimeout(initQuantityDiscountSelector, 1000);
});
</script>
